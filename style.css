/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    line-height: 1.6;
    color: #333;
    direction: rtl;
    text-align: right;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Hero Section */
.hero {
    height: 100vh;
    background: linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.4)),
                url('https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=1920&h=1080&fit=crop') center/cover;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    color: white;
    text-align: center;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, rgba(0,100,0,0.3), rgba(0,50,100,0.3));
}

.hero-content {
    position: relative;
    z-index: 2;
    max-width: 800px;
    padding: 0 20px;
}

.hero-title {
    font-family: 'Amir<PERSON>', serif;
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
    animation: fadeInUp 1s ease-out;
}

.hero-subtitle {
    font-size: 1.3rem;
    margin-bottom: 2rem;
    opacity: 0.9;
    animation: fadeInUp 1s ease-out 0.3s both;
}

.cta-button {
    background: linear-gradient(45deg, #2ecc71, #27ae60);
    color: white;
    border: none;
    padding: 15px 40px;
    font-size: 1.2rem;
    font-family: 'Cairo', sans-serif;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(46, 204, 113, 0.3);
    animation: fadeInUp 1s ease-out 0.6s both;
}

.cta-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(46, 204, 113, 0.4);
}

.cta-button i {
    margin-left: 10px;
}

.scroll-indicator {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    animation: bounce 2s infinite;
}

.scroll-indicator i {
    font-size: 2rem;
    opacity: 0.7;
}

/* Countries Section */
.countries {
    padding: 80px 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.section-title {
    font-family: 'Amiri', serif;
    font-size: 2.5rem;
    text-align: center;
    margin-bottom: 3rem;
    color: #2c3e50;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 3px;
    background: linear-gradient(45deg, #2ecc71, #27ae60);
    border-radius: 2px;
}

/* Table Styles */
.table-container {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    margin-top: 4rem;
    overflow-x: auto;
}

.countries-table {
    width: 100%;
    border-collapse: collapse;
    font-family: 'Cairo', sans-serif;
}

.countries-table thead {
    background: linear-gradient(45deg, #2c3e50, #34495e);
    color: white;
}

.countries-table th {
    padding: 20px 15px;
    text-align: center;
    font-family: 'Amiri', serif;
    font-size: 1.1rem;
    font-weight: 700;
    border-bottom: 3px solid #2ecc71;
}

.countries-table tbody tr {
    transition: all 0.3s ease;
    border-bottom: 1px solid #e9ecef;
}

.countries-table tbody tr:hover {
    background: linear-gradient(45deg, rgba(46, 204, 113, 0.05), rgba(39, 174, 96, 0.05));
    transform: scale(1.01);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.countries-table tbody tr:nth-child(even) {
    background: rgba(248, 249, 250, 0.5);
}

.countries-table td {
    padding: 20px 15px;
    vertical-align: top;
    border-right: 1px solid #e9ecef;
}

.countries-table td:last-child {
    border-right: none;
}

.country-name h3 {
    font-family: 'Amiri', serif;
    font-size: 1.4rem;
    color: #2c3e50;
    margin-bottom: 5px;
    font-weight: 700;
}

.country-subtitle {
    color: #27ae60;
    font-weight: 600;
    font-size: 0.9rem;
    display: block;
}

.country-description {
    color: #666;
    line-height: 1.6;
    font-size: 0.95rem;
    max-width: 300px;
}

.country-highlights {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
}

.highlight-tag {
    display: inline-block;
    background: linear-gradient(45deg, #3498db, #2980b9);
    color: white;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 0.75rem;
    margin: 2px 0;
    white-space: nowrap;
}

.best-time {
    color: #27ae60;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
}

.best-time i {
    color: #2ecc71;
}

.country-image {
    text-align: center;
}

.country-image img {
    width: 120px;
    height: 80px;
    object-fit: cover;
    border-radius: 10px;
    box-shadow: 0 4px 10px rgba(0,0,0,0.2);
    transition: all 0.3s ease;
}

.country-image img:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 15px rgba(0,0,0,0.3);
}

/* Highlights Section */
.highlights {
    padding: 80px 0;
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
}

.highlights .section-title {
    color: white;
}

.highlights-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    margin-top: 4rem;
}

.highlight-item {
    text-align: center;
    padding: 30px 20px;
    background: rgba(255,255,255,0.1);
    border-radius: 15px;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.highlight-item:hover {
    transform: translateY(-5px);
    background: rgba(255,255,255,0.15);
}

.highlight-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(45deg, #2ecc71, #27ae60);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    font-size: 2rem;
}

.highlight-item h3 {
    font-family: 'Amiri', serif;
    font-size: 1.3rem;
    margin-bottom: 15px;
}

.highlight-item p {
    opacity: 0.9;
    line-height: 1.6;
}

/* Footer */
.footer {
    background: #1a1a1a;
    color: white;
    padding: 40px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    margin-bottom: 30px;
}

.footer-section h3,
.footer-section h4 {
    font-family: 'Amiri', serif;
    margin-bottom: 15px;
    color: #2ecc71;
}

.social-links {
    display: flex;
    gap: 15px;
}

.social-links a {
    width: 40px;
    height: 40px;
    background: linear-gradient(45deg, #2ecc71, #27ae60);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-links a:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(46, 204, 113, 0.3);
}

.footer-bottom {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid #333;
    opacity: 0.7;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateX(-50%) translateY(0);
    }
    40% {
        transform: translateX(-50%) translateY(-10px);
    }
    60% {
        transform: translateX(-50%) translateY(-5px);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1.1rem;
    }

    .highlights-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
    }

    .section-title {
        font-size: 2rem;
    }

    .container {
        padding: 0 15px;
    }

    /* Table responsive design */
    .table-container {
        margin: 2rem 0;
        border-radius: 10px;
    }

    .countries-table {
        font-size: 0.9rem;
    }

    .countries-table th,
    .countries-table td {
        padding: 15px 10px;
    }

    .country-name h3 {
        font-size: 1.2rem;
    }

    .country-description {
        font-size: 0.85rem;
        max-width: 250px;
    }

    .highlight-tag {
        font-size: 0.7rem;
        padding: 4px 8px;
    }

    .country-image img {
        width: 100px;
        height: 70px;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }

    .cta-button {
        padding: 12px 30px;
        font-size: 1rem;
    }

    .countries {
        padding: 60px 0;
    }

    .highlights {
        padding: 60px 0;
    }

    /* Mobile table layout */
    .countries-table,
    .countries-table thead,
    .countries-table tbody,
    .countries-table th,
    .countries-table td,
    .countries-table tr {
        display: block;
    }

    .countries-table thead tr {
        position: absolute;
        top: -9999px;
        left: -9999px;
    }

    .countries-table tr {
        background: white;
        border: 1px solid #e9ecef;
        border-radius: 10px;
        margin-bottom: 15px;
        padding: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    .countries-table td {
        border: none;
        border-bottom: 1px solid #e9ecef;
        position: relative;
        padding: 10px 0 10px 40%;
        text-align: right;
    }

    .countries-table td:before {
        content: attr(data-label);
        position: absolute;
        left: 6px;
        width: 35%;
        padding-right: 10px;
        white-space: nowrap;
        font-weight: bold;
        color: #2c3e50;
        font-family: 'Amiri', serif;
    }

    .country-image {
        text-align: center;
        padding: 15px 0;
    }

    .country-image img {
        width: 150px;
        height: 100px;
    }
}
